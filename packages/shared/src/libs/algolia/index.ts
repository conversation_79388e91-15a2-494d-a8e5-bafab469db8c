import { stripHtml } from '../string-utils'
import { transformFeedToSearchObject, transformLibraryToSearchObject } from './transform-data-search'
import { searchAdminClient } from '@/shared-config/algolia-client'
import prisma from '@/shared-libs/prisma/prisma'
import { getFullName } from '@/shared-libs/profile'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'
import { IFeed, IUser } from '@/shared-types/interfaces'

export const updateAlgoliaMembersIndexForUserId = async (userId: string) => {
  const user = await prisma.user.findUnique({
    where: {
      id: userId,
    },
    select: {
      id: true,
      email: true,
      first_name: true,
      last_name: true,
      username: true,
      image: true,
      status: true,
      profile: true,
      role: true,
      createdAt: true,
      updatedAt: true,
      user_memberships: {
        where: {
          status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
        },
      },
    },
  })
  if (!user) {
    return
  }

  const membershipIds = user.user_memberships.map((um) => um.membership_id)
  const searchObject = {
    objectID: user.id,
    first_name: user.first_name,
    last_name: user.last_name,
    name: getFullName(user.first_name, user.last_name),
    image: user.profile?.image || user.image,
    image_crop_area: user.profile?.image_crop_area,
    role: user.role,
    status: user.status,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
    username: user.username,
    viewable_by: membershipIds,
  }
  const index = searchAdminClient.initIndex('member')
  index.saveObject(searchObject).wait()
}

export const uploadDataToAlgoliaIndex = async (
  indexName: 'member' | 'feed' | 'content-library',
  data: (Partial<IUser> & { id: string }) | Partial<IFeed> | any,
) => {
  let transformed: any = null
  if (indexName === 'feed') {
    transformed = await transformFeedToSearchObject(data as IFeed)
  } else if (indexName === 'content-library') {
    data.text = stripHtml(data.text || '')
    transformed = await transformLibraryToSearchObject(data as any)
  }

  if (transformed) {
    const index = searchAdminClient.initIndex(indexName)
    index.saveObject(transformed).wait()
  }
}

export const deleteRecordsFromAlgoliaIndex = async (
  indexName: 'member' | 'feed' | 'content-library',
  ids: string[],
) => {
  const index = searchAdminClient.initIndex(indexName)
  if (ids.length > 1) {
    index.deleteObjects(ids).wait()
  } else {
    index.deleteObject(ids[0]).wait()
  }
}
