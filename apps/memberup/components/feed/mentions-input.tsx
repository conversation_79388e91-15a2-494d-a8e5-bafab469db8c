// @ts-nocheck
import Editor from '@draft-js-plugins/editor'
import createMentionPlugin from '@draft-js-plugins/mention'
import clsx from 'clsx'

import editorStyles from '@/components/feed/editor-styles.module.css'
import EmojiPickerWithButton from '@/memberup/components/common/pickers/emoji-mart-picker-with-button'
import { insertEmoji, isEditorEmpty } from '@/memberup/libs/mentions'

import '@draft-js-plugins/mention/lib/plugin.css'

import ArrowCircleRightIcon from '@mui/icons-material/ArrowCircleRight'
import { Box, IconButton } from '@mui/material'
import { EditorState } from 'draft-js'

import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'

import 'draft-js/dist/Draft.css'

import { ReactElement, useCallback, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'

import createLinkPlugin from '../../src/components/common/editor/link-plugin'
import Portal from '../../src/components/Portal'
import mentionStyles from '@/components/feed/mentions-styles.module.css'
import { useStore } from '@/hooks/useStore'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'

function Entry(props): ReactElement {
  const { isDarkTheme, mention, theme, searchValue, isFocused, selectMention, ...parentProps } = props.entryProps

  /*  ATENTION: parentProps.onMouseEnter generates flickering if not cached */

  return (
    <div {...parentProps}>
      <div className={theme?.mentionSuggestionsEntryContainer}>
        <div className={theme?.mentionSuggestionsEntryContainerLeft} style={{ display: 'flex', alignItems: 'center' }}>
          <AppProfileImage imageUrl={mention.image} cropArea={mention.image_crop_area} name={mention.name} size={28} />
          <span className={theme?.mentionSuggestionsEntryText} style={{ marginLeft: 10 }}>
            {mention.name}
          </span>
        </div>
      </div>
    </div>
  )
}

const MentionsInput = (props) => {
  const {
    editorState,
    formState,
    isPosting = false,
    placeholder,
    showLoadingSpinner = false,
    onMentionStateChange,
    type = 'post',
    users,
    onCancel,
    onChange,
    onChangeEditorState,
    onSubmit,
    portal = false,
    portalTargetId = 'portal-suggestions-root',
    focus = false,
    styles = {},
  } = props

  const { theme, isMobile } = useAppTheme()
  const [openMentionSuggestions, setOpenMentionSuggestions] = useState(false)
  const [suggestions, setSuggestions] = useState(users)

  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)
  const [parentPortal, setParentPortal] = useState(null)
  const editorRef = useRef()

  // Updated styles for the container
  const containerStyle = {
    display: 'flex',
    alignItems: type === 'post' ? 'inherit' : 'center',
    maxHeight: '50vh',
  }

  const editorContainerStyle = {
    fontSize: '0.875rem',
    flexGrow: 1,
    //maxWidth: type === 'post' ? '38vw' : type === 'comment' ? '36.5vw' : '34vw',
    maxWidth: '100%',
    overflowWrap: 'anywhere',
    maxHeight: type === 'post' ? '' : '50vh',
    overflowY: 'auto',
    ...styles,
    //minWidth: type === 'post' ? '' : type === 'comment' ? '540px' : '490px',
  }

  const mentionAdditionalStyles = {
    color: theme.palette.primary.main,
    lineHeight: '20px',
    fontFamily: 'Graphik Semibold',
    fontWeight: 400,
  }

  useLayoutEffect(() => {
    if (editorRef.current && focus) {
      setTimeout(() => {
        if (editorRef.current) {
          editorRef.current.focus()
        }
      }, 0)
    }
  }, [focus, editorRef])

  useEffect(() => {
    if (!portal) return
    setParentPortal(true)
  }, [])

  const { MentionSuggestions, plugins } = useMemo(() => {
    const mentionPlugin = createMentionPlugin({
      mentionComponent(mentionProps) {
        return <span style={mentionAdditionalStyles}>@{mentionProps.children}</span>
      },
      popperOptions: {
        placement: 'top',
        modifiers: [
          {
            name: 'offset',
            options: {
              offset: [100, 0],
            },
          },
        ],
      },
      theme: {
        ...mentionStyles,
        backgroundColor: '#fff !important',
        fontFamily: 'Graphik Regular',
        fontWeight: 500,
        fontSize: '14px',
        mentionSuggestions: isDarkTheme
          ? `${mentionStyles.mentionSuggestions} ${mentionStyles.mentionSuggestionsDark}`
          : mentionStyles.mentionSuggestions,
        mentionSuggestionsEntryFocused: isDarkTheme
          ? `${mentionStyles.mentionSuggestionsEntryFocusedDark}`
          : mentionStyles.mentionSuggestionsEntryFocused,
      },
    })

    const linkPlugin = createLinkPlugin()
    const plugins = [mentionPlugin, linkPlugin]
    const { MentionSuggestions } = mentionPlugin

    return { plugins, MentionSuggestions }
  }, [])

  const onMentionSuggestionsOpenChange = useCallback(
    (_open: boolean) => {
      setOpenMentionSuggestions(_open)
      if (onMentionStateChange) {
        onMentionStateChange(_open)
      }
    },
    [onMentionStateChange],
  )

  const onMentionSuggestionsSearchChange = useCallback(
    ({ trigger, value }: { trigger: string; value: string }) => {
      if (trigger === '@') {
        const suggestionsFilter = (searchValue, mentions) => {
          const lowerCaseSearchValue = searchValue.toLowerCase()

          return mentions.filter((mention) => mention?.name?.toLowerCase().startsWith(lowerCaseSearchValue))
        }

        setSuggestions(suggestionsFilter(value, users))
      }
    },
    [users],
  )

  const handleChange = (editorState: EditorState) => {
    onChangeEditorState(editorState)
    onChange(editorState.getCurrentContent())
  }

  const contentStyle = useMemo(
    () => ({
      position: 'relative',
      minHeight: type === 'post' ? '150px' : '40px',
      backgroundColor: type === 'post' ? 'transparent' : isDarkTheme ? '#292A2E' : '#F2F2F3',
      border: 'none',
      padding: type === 'post' ? '' : '10px 16px',
      lineHeight: '20px',
      boxShadow: 'none',
      fontSize: isMobile ? '16px' : '14px',
      '&:focusVisible': {
        outline: 'none',
      },
      wordBreak: 'break-word',
    }),
    [type, isDarkTheme],
  )

  const buttonContainerStyle = {
    display: 'flex',
    justifyContent: 'flex-end',
    position: 'sticky',
    bottom: 0,
  }

  const buttonStyle = {
    cursor: 'pointer !important',
  }

  const suggestionListStyle = {
    borderRadius: '12px',
  }

  const createEntryComponent = useCallback(
    (entryProps) => {
      return <Entry entryProps={{ ...entryProps, isDarkTheme }} />
    },
    [isDarkTheme],
  )

  const editorHasNoData = isEditorEmpty(editorState)
  const suggestionList = suggestions.slice(0, 7)
  return (
    <div className={clsx(editorStyles.editor, 'text-editor')} style={{ ...containerStyle, ...contentStyle }}>
      <>
        {type !== 'post' ? (
          <Box sx={{ ml: '-15px', mr: '5px' }}>
            <EmojiPickerWithButton
              onSelectedEmoji={(e: any) => insertEmoji(e, editorState, onChangeEditorState)}
              theme={isDarkTheme ? 'dark' : 'light'}
              styles={isDarkTheme ? '#8D94A3' : '#585D66'}
            />
          </Box>
        ) : null}
        <div style={editorContainerStyle}>
          <Editor
            disabled={isPosting}
            editorKey={'editor'}
            editorState={editorState}
            onChange={handleChange}
            /*   handleReturn={handleReturn} */
            keyBindingFn={(event) => {
              if (event.type === 'keydown' && event.code === 'Escape') {
                onCancel?.()
              }
            }}
            plugins={plugins}
            ref={editorRef}
            placeholder={placeholder}
            editorStyle={contentStyle}
            stripPastedStyles={true}
            readOnly={isPosting}
          />
        </div>
        {onSubmit && (
          <div style={buttonContainerStyle}>
            <IconButton
              color="primary"
              size="small"
              type="submit"
              style={buttonStyle}
              onClick={onSubmit}
              disabled={(!formState?.isValid && type === 'post') || isPosting || editorHasNoData}
            >
              <ArrowCircleRightIcon sx={{ color: theme.palette.mode === 'dark' ? '#8e94a2' : '#595d65' }} />
            </IconButton>
          </div>
        )}
      </>
      {!portal && (
        <div style={{ color: 'black' }}>
          <div className="suggestion-list" style={suggestionListStyle}>
            <MentionSuggestions
              open={openMentionSuggestions}
              onOpenChange={onMentionSuggestionsOpenChange}
              suggestions={suggestionList}
              onSearchChange={onMentionSuggestionsSearchChange}
              entryComponent={createEntryComponent}
            />
          </div>
        </div>
      )}

      {portal && parentPortal && (
        <Portal target={portalTargetId}>
          <div className="suggestion-list" style={suggestionListStyle}>
            <MentionSuggestions
              open={openMentionSuggestions}
              onOpenChange={onMentionSuggestionsOpenChange}
              suggestions={suggestionList}
              onSearchChange={onMentionSuggestionsSearchChange}
              entryComponent={createEntryComponent}
            />
          </div>
        </Portal>
      )}
    </div>
  )
}

export default MentionsInput
