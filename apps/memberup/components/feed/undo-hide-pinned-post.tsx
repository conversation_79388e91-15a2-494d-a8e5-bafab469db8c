import Box from '@mui/material/Box'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import React from 'react'

import SVGPushPinLargeFilled from '../../src/components/svgs/push-pin-large-filled'
import { Button } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { hidePinnedPostApi } from '@/shared-services/apis/user.api'

const useStyles = makeStyles((theme) => ({
  container: {
    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(33,33,36, 1)' : 'rgba(255,255,255, 1)',
    borderRadius: '16px',
    marginTop: '16px',
    marginBottom: '16px',
    alignItems: 'center',
    padding: '16px 16px 16px 26px',
    background: '',
  },
  title: {
    opacity: 1,
    color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 1)' : '#000',
    fontFamily: 'Graphik Semibold',
    fontSize: '14px',
    fontWeight: 500,
    textAlign: 'left',
    lineHeight: '20px',
    marginBottom: '4px',
  },
  description: {
    color: 'rgba(141, 148, 163, 1)',
    fontFamily: '"Graphik Regular"',
    fontSize: '14px',
    fontWeight: 400,
    fontStyle: 'regular',
    letterSpacing: '0px',
    textAlign: 'left',
    lineHeight: '20px',
  },
}))

export default function UndoHidePinnedPost({ postId, onUndo }) {
  const [requestUnhide, setrequestUnhide] = React.useState(false)
  const { updateProfile } = useStore((state) => state.auth)
  const classes = useStyles()
  const handleUndo = async () => {
    setrequestUnhide(true)
    const result = await hidePinnedPostApi({ hide: false, post_id: postId })
    if (result.data.success) {
      updateProfile({ pinned_posts_hidden: result.data.data })
    }
    onUndo()
    setrequestUnhide(false)
  }

  return (
    <Stack className={classes.container} direction={'row'}>
      <Stack sx={{ flexGrow: 1 }} direction={'row'}>
        <Box style={{ marginRight: '15px', top: '2px', position: 'relative' }}>
          <SVGPushPinLargeFilled />
        </Box>
        <Box style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
          <Typography className={classes.title}>Post hidden</Typography>
          <Typography className={classes.description}>You won't see this post in your Community Feed.</Typography>
        </Box>
      </Stack>
      <Button disabled={requestUnhide} loading={requestUnhide} onClick={handleUndo}>
        Undo
      </Button>
    </Stack>
  )
}
