import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status403 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'
import { findUserMembershipAsAdminOrCreator } from '@/shared-libs/prisma/user-membership'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { user_id: memberId, membership_id: membershipId } = req.body

    const ownerUserMembership = findUserMembershipAsAdminOrCreator(user.id, membershipId)
    if (!ownerUserMembership) {
      return status403(res, `You don't have permissions to manage members of the community.`)
    }

    // Get the user membership or return status400
    let userMembership = await prisma.userMembership.findFirst({
      where: {
        user_id: memberId,
        membership_id: membershipId,
      },
    })
    if (!userMembership) {
      return status400(res, 'The user does not belong to the membership.')
    }

    const updatedUserMembership = await prisma.userMembership.update({
      where: {
        id: userMembership.id,
      },
      data: {
        status: USER_MEMBERSHIP_STATUS_ENUM.banned,
      },
      include: {
        membership: true,
      },
    })
    return status200(res, updatedUserMembership)
  } catch (e) {
    console.error(e)
    return res.status(500).json({
      success: false,
      message: e.message,
    })
  }
})

export default handler
