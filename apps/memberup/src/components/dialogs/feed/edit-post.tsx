'use client'

import draftUtils from '@draft-js-plugins/utils'
import { IGif } from '@giphy/js-types'
import { joiResolver } from '@hookform/resolvers/joi'
import AddLinkIcon from '@mui/icons-material/AddLink'
import Box from '@mui/material/Box'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import Divider from '@mui/material/Divider'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import LinearProgress from '@mui/material/LinearProgress'
import Modal from '@mui/material/Modal'
import Slide from '@mui/material/Slide'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import { EditorState, Modifier, SelectionState } from 'draft-js'
import Joi from 'joi'
import dynamic from 'next/dynamic'
import Image from 'next/image'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { Controller, useForm } from 'react-hook-form'

import { convertToHtml, createEditorStateFromHtml } from '../../common/editor/editor-utils'
import AddLinkDialog from './edit-post-link-dialog'
import EditPostLinkPreview from './edit-post-link-preview'
import { AppImg } from '@memberup/shared/src/components/common/media/image'
import { AppVideo } from '@memberup/shared/src/components/common/media/video'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { getFileType, getThumbnailForVideo } from '@memberup/shared/src/libs/file'
import { uploadFileToCloudinaryApi } from '@memberup/shared/src/services/apis/cloudinary.api'
import { ACCEPT_ALL, IMAGE_ACCEPT_ONLY, VIDEO_ACCEPT_ONLY } from '@memberup/shared/src/types/consts'
import { FEED_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IFeed } from '@memberup/shared/src/types/interfaces'
import MentionsInput from '@/components/feed/mentions-input'
import { GiphyGif } from '@/components/images/giphy-gif'
import { Button } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import AppSearchGif from '@/memberup/components/common/app-search-gif'
import EmojiPickerWithButton from '@/memberup/components/common/pickers/emoji-mart-picker-with-button'
import SpaceSelector from '@/memberup/components/dialogs/feed/space-selector'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import useUploadFiles from '@/memberup/components/hooks/use-upload-files'
import SVGCloseNew from '@/memberup/components/svgs/close-new'
import SVGCloseSmall from '@/memberup/components/svgs/close-small'
import SVGFileNew from '@/memberup/components/svgs/file-new'
import SVGGifNew from '@/memberup/components/svgs/gif-new'
import SVGPhotoNew from '@/memberup/components/svgs/photo-new'
import SVGVideoNew from '@/memberup/components/svgs/video-new'
import { base64toImgFile } from '@/memberup/libs/attachments'
import { insertEmoji, isEditorEmpty, mentionGetEditorMentions, mentionGetSuggestions } from '@/memberup/libs/mentions'
import {
  selectCurrentPostDraft,
  selectRequestUpsertFeed,
  setCurrentPostDraft,
  setRequestUpsertFeed,
  upsertFeedFailure,
  upsertFeedSuccess,
} from '@/memberup/store/features/feedSlice'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import { shortenFileName, stripHtml } from '@/shared-libs/string-utils'
import { createFeedApi, updateFeedApi } from '@/shared-services/apis/feed.api'
import { IMembership, IUser } from '@/shared-types/interfaces'

const PDFtoImage = dynamic(() => import('@/memberup/components/common/pdf-to-image'), {
  ssr: false,
})
const IMG_FILE_MAX_SIZE = parseInt(process.env.NEXT_PUBLIC_IMG_FILE_MAX_SIZE)
const VIDEO_FILE_MAX_SIZE = parseInt(process.env.NEXT_PUBLIC_VIDEO_FILE_MAX_SIZE)

type FormDataType = {
  text: string
  attachments: {
    filename: string
    mimetype: string
    passthrough?: string
    title?: string
    url: string
    uploaded_date: string
    thumbnail?: string
    size_in_bytes?: number
  }[]
  attachment_files: (File & Partial<IGif> & { is_gif?: boolean })[]
  title: string
}

const FormSchema = Joi.object({
  title: Joi.string().required().messages({
    'string.empty': 'Please enter a title.',
    'any.required': 'Please enter a title.',
  }),
  text: Joi.string().allow('', null),
}).options({ allowUnknown: true })

const useStyles = makeStyles((theme) => ({
  focusVisible: {
    '&:focus-visible': {
      outline: 'none',
    },
  },
  attachmentOther: {
    position: 'relative',
    borderRadius: '12px',
    border: '1px solid',
    borderColor: theme.palette.divider,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 42,
    lineHeight: 0,
    overflow: 'hidden',
    marginRight: '8px',
    marginBottom: '8px',
    width: 112,
    height: 112,
    '& .MuiIconButton-root': {
      backgroundColor: '#17171a',
      color: '#8d94a3',
      position: 'absolute',
      right: '3px',
      top: '3px',
      padding: '6px',
      width: '22px',
      height: '22px',
      '&:hover': {
        display: 'inline-flex',
        backgroundColor: 'rgba(23, 23, 26, 0.75) !important',
      },
    },
  },
  attachment: {
    position: 'relative',
    borderRadius: '12px',
    border: '1px solid',
    borderColor: theme.palette.divider,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 42,
    lineHeight: 0,
    overflow: 'hidden',
    marginRight: '8px',
    marginBottom: '8px',
    width: 112,
    height: 112,
    '& img': {
      height: '100%',
      width: '100%',
      objectFit: 'cover',
    },
    '& .MuiIconButton-root': {
      backgroundColor: '#17171a',
      color: '#8d94a3',
      position: 'absolute',
      right: '3px',
      top: '3px',
      padding: '6px',
      width: '22px',
      height: '22px',
      '&:hover': {
        display: 'inline-flex',
        backgroundColor: 'rgba(23, 23, 26, 0.75) !important',
      },
    },
  },
  iconButton: {
    width: 42,
    height: 42,
    padding: '10px',
    borderRadius: '16px',
    color: theme.palette.mode === 'dark' ? '#8D94A3' : '#585D66',
    '&:hover': {
      color: theme.palette.mode === 'dark' ? '#fff' : '#000',
    },
  },
  selectedIconButton: {
    width: 42,
    height: 42,
    padding: '10px',
    borderRadius: '16px',
    backgroundColor: 'rgba(255, 255, 255, 0.08)',
    color: theme.palette.primary.main,
  },
  dropText: {
    position: 'absolute',
    left: 0,
    top: 160,
    width: '100%',
    textAlign: 'center',
    zIndex: 1,
  },
}))

export const VideoThumbnail = ({ file }) => {
  const mountedRef = useMounted(true)
  const [src, setSrc] = useState('')

  useEffect(() => {
    const getThumbnail = async () => {
      const temp = await getThumbnailForVideo(file)
      if (mountedRef.current) {
        setSrc(temp)
        const videoThumbnailBlob = base64toImgFile(temp, file.name)
        const uploadedThumbnail = await uploadFileToCloudinaryApi(videoThumbnailBlob, 'image')
        file.thumbnail = uploadedThumbnail.data.secure_url
        file.size_in_bytes = file.size
      }
    }
    if (file instanceof File) {
      getThumbnail()
    }
  }, [file])

  if (!src) return null
  return <AppImg src={src} height={100} width={100} alt="No Attachment Image" />
}

const EditPost: React.FC<{
  membership: IMembership
  open: boolean
  data?: IFeed
  mode: string
  onClose: () => void
}> = ({ mode, open, data, onClose, membership }) => {
  const classes = useStyles()
  const mountedRef = useMounted(true)
  const formSubmittedRef = useRef(false)
  const selectionRef = useRef({
    start: 1,
    end: -1,
  })
  const { theme, isDarkTheme, isMobile } = useAppTheme()
  const dispatch = useAppDispatch()
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const slug = searchParams.get('slug')
  const user = useStore((state) => state.auth.user)
  const userProfile = useStore((state) => state.auth.profile)
  const userFullName = useStore((state) => state.auth.userFullName)
  const { isCurrentUserAdmin: isAdminOrCreator } = useCheckUserRole()
  const [selectedSpace, setSelectedSpace] = useState(null)
  const [error, setError] = useState(null)
  const requestUpsertFeed = useAppSelector((state) => selectRequestUpsertFeed(state))
  const [acceptFileType, setAcceptFileType] = useState(ACCEPT_ALL)
  const [openFileDialog, setOpenFileDialog] = useState(false)
  const [openSearchGif, setOpenSearchGif] = useState(false)
  const { uploadProgress, initUploadFiles, handleUploadFiles } = useUploadFiles('feed', membership.id)
  const [mentionedUsers, setMentionedUsers] = useState([])
  const members = useAppSelector((state) => selectMembersMap(state))
  const [formattedSuggestions, setFormattedSuggestions] = useState([])
  const currentPostDraft = useAppSelector((state) => selectCurrentPostDraft(state))
  const [showLinkDialog, setShowLinkDialog] = useState(false)
  const [currentLinkUrl, setCurrentLinkUrl] = useState('')
  const [currentLinkText, setCurrentLinkText] = useState('')
  const hasSetMembersRef = useRef(false)
  const previousPaletteModeRef = useRef(null)

  const [editorState, setEditorState] = useState(
    (mode === 'edit'
      ? data
        ? null
        : () => EditorState.createEmpty()
      : currentPostDraft
        ? () => currentPostDraft.editorState
        : EditorState.createEmpty()) as EditorState,
  )

  const feed = useStore((store) => store.feed)

  const spaces = membership.channels.filter((c) => c.visibility && (!c.is_private || isAdminOrCreator))

  const {
    control,
    getValues,
    setValue,
    formState: { isDirty, isValid },
    register,
    reset,
    watch,
    handleSubmit,
  } = useForm<FormDataType>({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues: {
      title: mode === 'edit' ? data?.title || '' : (currentPostDraft?.title ?? ''),
      text: data?.text || '',
      attachments: mode === 'edit' ? data?.attachments || [] : (currentPostDraft?.attachments ?? []),
      attachment_files: mode === 'edit' ? [] : (currentPostDraft?.attachment_files ?? []),
    },
    resolver: joiResolver(FormSchema),
  })
  const text = watch('text')
  const title = watch('title')
  const attachments = watch('attachments')
  const attachmentFiles = watch('attachment_files')

  const onDrop = useCallback((acceptedFiles) => {
    setAcceptFileType(ACCEPT_ALL)
    setOpenFileDialog(false)
    const acceptedFile = acceptedFiles?.[0]
    if (!acceptedFile) return
    const fileType = getFileType(acceptedFile)
    if (fileType === 'video') {
      if (acceptedFile.size > VIDEO_FILE_MAX_SIZE) {
        toast.error(`The video file size needs to be ${VIDEO_FILE_MAX_SIZE / 1024 / 1024 / 1024} GB or less.`)
        return
      }
    } else if (acceptedFile.size > IMG_FILE_MAX_SIZE) {
      toast.error(
        `The ${fileType === 'image' ? 'image file' : 'file'} size needs to be ${
          IMG_FILE_MAX_SIZE / 1024 / 1024
        } MB or less.`,
      )
      return
    }

    const temp = getValues('attachment_files')
    setValue('attachment_files', [...temp, acceptedFile], {
      shouldValidate: true,
      shouldDirty: true,
    })
    setOpenFileDialog(false)
    // Do something with the files
  }, [])

  const onFileDialogCancel = useCallback(() => {
    setOpenFileDialog(false)
  }, [])

  const {
    getRootProps,
    getInputProps,
    isDragActive,
    open: openFileSelect,
  } = useDropzone({
    onDrop,
    onFileDialogCancel,
    maxFiles: 1,
    multiple: false,
    accept: acceptFileType,
    noClick: true,
  })

  const getChannel = function (router, channels) {
    if (pathname === '/community') {
      return null
    }
    return spaces.find((s) => s.slug === slug)
  }

  useEffect(() => {
    if (!mountedRef.current) return

    initUploadFiles()

    return () => {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open, data])

  useEffect(() => {
    if (requestUpsertFeed) {
      formSubmittedRef.current = requestUpsertFeed
      return
    }
    if (!formSubmittedRef.current) return
    onClose()

    return () => {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requestUpsertFeed])

  useEffect(() => {
    if (data?.text) {
      //const newEditorState = mentionCreateEditorState(data?.text, suggestions)
      const newEditorState = createEditorStateFromHtml(data?.text)
      try {
        setEditorState(newEditorState)
      } catch (e) {
        console.log('error', e.message)
      }
    }
    if (data?.id) {
      const spaceId = data.cid.split(':')[1]
      const space = spaces.find((s) => s.id === spaceId)
      setSelectedSpace(space)
    } else {
      const space = getChannel(router, spaces)
      setSelectedSpace(space)
    }
  }, [data])

  useEffect(() => {
    if (user?.id && members && (!hasSetMembersRef.current || previousPaletteModeRef.current !== theme.palette.mode)) {
      hasSetMembersRef.current = true
      previousPaletteModeRef.current = theme.palette.mode
      const suggestions = mentionGetSuggestions(
        { ...members, [user.id]: members[user.id] },
        user as IUser,
        membership.id,
      )
      setFormattedSuggestions(suggestions)
    }
  }, [user, members, theme.palette.mode])

  useEffect(() => {
    if (openFileDialog && mountedRef.current) {
      openFileSelect()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [openFileDialog])

  const handleFormSubmit = async (formData: FormDataType) => {
    if (!selectedSpace) {
      setError('You must choose a space before posting')
      return
    }

    let uploadedAttachments = []
    if (formData.attachment_files.length > 0) {
      uploadedAttachments = await handleUploadFiles(formData.attachment_files, 'Cloudinary')
    }

    /* remove from mentionedUsers the property markup from each object and return the array of objects */

    const filteredMentionedUsers = mentionedUsers.map((user) => {
      const { markup, imageUrl, cropArea, ...rest } = user
      return rest
    })

    const htmlText = convertToHtml(editorState)
    const plainText: string = stripHtml(htmlText)
    if (plainText.trim() === '') {
      setError('Please insert some content')
      return
    }

    const payload = {
      title: formData.title,
      text: htmlText,
      attachments: [...formData.attachments, ...uploadedAttachments],
      mentioned_users: filteredMentionedUsers,
      host: window.location.protocol + '//' + window.location.host,
      membership_id: membership.id,
    }

    if (!data?.id) {
      payload['channel_id'] = selectedSpace.id
      payload['feed_type'] = FEED_TYPE_ENUM.default
    }

    try {
      dispatch(setRequestUpsertFeed({ value: true }))
      const result = data?.id ? await updateFeedApi(data.id, payload) : await createFeedApi(payload, membership.id)

      if (result?.data?.success) {
        dispatch(
          upsertFeedSuccess({
            data: result.data.data,
          }),
        )
        if (data?.id) {
          toast.success('Post updated successfully')
          feed.updateMessage(result.data.data.cid.split(':')[1], result.data.data)
        } else {
          toast.success('Post created successfully')
          feed.addMessageToFront(result.data.data.cid.split(':')[1], result.data.data)
        }

        onClose()
        dispatch(setCurrentPostDraft(null))
        setEditorState(EditorState.createEmpty())
        reset()
      } else {
        const message = data?.id ? 'Failed to update post' : 'Failed to create post'
        toast.error(message)
        dispatch(
          upsertFeedFailure({
            status: 400,
            message: message,
          }),
        )
      }
    } catch (error) {
      console.error(error)
      toast.error(formSubmitError)
      dispatch(
        upsertFeedFailure({
          status: error.response?.status,
          message: error.response?.data?.message,
        }),
      )
    }
  }

  const removePreview = (editorState: EditorState, entityKey: string) => {
    const contentState = editorState.getCurrentContent()
    const updatedContentState = contentState.mergeEntityData(entityKey, { preview: false })

    const updatedEditorState = EditorState.push(editorState, updatedContentState, 'apply-entity')
    const selectionState = updatedEditorState.getSelection()
    return EditorState.forceSelection(updatedEditorState, selectionState)
  }

  const renderedLinkPreviews = useMemo(() => {
    if (!editorState) {
      return
    }
    const contentState = editorState.getCurrentContent()
    const components = []
    contentState.getBlockMap().forEach((block) => {
      block.findEntityRanges(
        (character) => {
          return character.getEntity() !== null
        },
        (start, end) => {
          const entityKey = block.getEntityAt(start)
          const entityType = contentState.getEntity(entityKey).getType()
          const entityData = contentState.getEntity(entityKey).getData()
          const handleRemovePreview = (entityKey) => {
            setEditorState((prevEditorState) => removePreview(prevEditorState, entityKey))
          }

          const handleLinkLoad = (entityKey, linkData) => {
            // TODO: Update the editor state to add link data
          }

          if (entityData.preview) {
            components.push(
              <EditPostLinkPreview
                key={entityKey}
                url={entityData.url}
                onLoad={(linkData) => handleLinkLoad(entityKey, linkData)}
                onClose={() => {
                  handleRemovePreview(entityKey)
                }}
              />,
            )
          }
        },
      )
    })
    return components
  }, [editorState])

  const renderAttachments = useMemo(() => {
    if (!attachments.length && !attachmentFiles.length) return null
    const temp = []
    const addAttachmentToRender = (key, name, mimetype, url, file, handleDelete) => {
      if (mimetype?.includes('image')) {
        temp.push(
          <Box key={key} className={classes.attachment}>
            <AppImg src={url || URL.createObjectURL(file)} height={100} width={100} alt="No Attachment Image" />
            <IconButton
              size="small"
              aria-label="delete attachment"
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                handleDelete()
              }}
            >
              <SVGCloseSmall />
            </IconButton>
          </Box>,
        )
      } else if (mimetype?.includes('application/pdf') || mimetype?.includes('pdf')) {
        temp.push(
          <Box key={key} className={classes.attachmentOther} sx={{ width: '18px', height: '24px' }}>
            <Image
              style={{ marginTop: '15px' }}
              width="18"
              height="24"
              alt="icon"
              src={`/assets/default/images/icons/pdf.png`}
            />
            <PDFtoImage
              pdfData={file} // Use the provided PDF file data
              onThumbnailGenerated={async (base64data) => {
                const pdfScreenshotBlob = base64toImgFile(base64data, file.name)

                // Update your attachments array with the file object
                file.size_in_bytes = file.size

                const uploadedThumbnail = await uploadFileToCloudinaryApi(pdfScreenshotBlob, 'image')
                file.thumbnail = uploadedThumbnail.data.secure_url
              }}
            />
            {Boolean(name) && (
              <Typography
                sx={{
                  mt: '4px',
                  color: '#8D94A3',
                  width: '100%',
                  fontSize: '12px',
                  p: '12px',
                }}
              >
                {shortenFileName(name, 25)}
              </Typography>
            )}
            <IconButton
              size="small"
              aria-label="delete attachment"
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                handleDelete()
              }}
            >
              <SVGCloseSmall />
            </IconButton>
          </Box>,
        )
      } else if (mimetype?.includes('video') && (file instanceof File || file.thumbnail)) {
        const child =
          file instanceof File ? <VideoThumbnail file={file} /> : <AppVideo url={file.thumbnail || ''} height={100} />
        temp.push(
          <Box key={key} className={classes.attachment}>
            {child}
            <IconButton
              size="small"
              aria-label="delete attachment"
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                handleDelete()
              }}
            >
              <SVGCloseSmall />
            </IconButton>
          </Box>,
        )
      } else if (mimetype?.includes('gif')) {
        temp.push(
          <Box key={key} className={classes.attachment}>
            <GiphyGif id={file.id} width={100} />
            <IconButton
              size="small"
              aria-label="delete attachment"
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                handleDelete()
              }}
            >
              <SVGCloseSmall />
            </IconButton>
          </Box>,
        )
      } else if (mimetype?.includes('text/csv') || mimetype?.includes('csv')) {
        temp.push(
          <Box key={key} className={classes.attachmentOther} sx={{ display: 'flex', alignItems: 'center' }}>
            <Image
              style={{ marginRight: '10px' }}
              width="18"
              height="24"
              alt="icon"
              src={`/assets/default/images/icons/csv.png`}
            />
            <Typography
              sx={{
                color: '#8D94A3',
                fontSize: '12px',
              }}
            >
              {shortenFileName(name, 25)}
            </Typography>
            <IconButton
              size="small"
              aria-label="delete attachment"
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                handleDelete()
              }}
            >
              <SVGCloseSmall />
            </IconButton>
          </Box>,
        )
      } else if (mimetype?.includes('other')) {
        temp.push(
          <Box key={key} className={classes.attachmentOther} sx={{ display: 'flex', alignItems: 'center' }}>
            <Image
              style={{ marginRight: '10px' }}
              width="18"
              height="24"
              alt="icon"
              src={`/assets/default/images/icons/other.png`}
            />
            <Typography
              sx={{
                color: '#8D94A3',
                fontSize: '12px',
              }}
            >
              {shortenFileName(name, 25)}
            </Typography>
            <IconButton
              size="small"
              aria-label="delete attachment"
              onClick={(e) => {
                e.stopPropagation()
                e.preventDefault()
                handleDelete()
              }}
            >
              <SVGCloseSmall />
            </IconButton>
          </Box>,
        )
      } else {
      }
    }

    for (let i = 0; i < attachments.length; i++) {
      if (attachments[i]?.mimetype && attachments[i]?.url) {
        addAttachmentToRender(
          `attachments-${i}`,
          attachments[i].filename,
          attachments[i].mimetype,
          attachments[i].url,
          attachments[i],
          () => {
            setValue(
              'attachments',
              attachments.filter((a, j) => j !== i),
              { shouldValidate: true, shouldDirty: true },
            )
          },
        )
      }
    }

    for (let i = 0; i < attachmentFiles.length; i++) {
      addAttachmentToRender(
        `attachment_files-${i}`,
        attachmentFiles[i].name,
        attachmentFiles[i]?.type,
        null,
        attachmentFiles[i],
        () => {
          setValue(
            'attachment_files',
            attachmentFiles.filter((a, j) => j !== i),
            { shouldValidate: true, shouldDirty: true },
          )
        },
      )
    }

    return (
      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
        }}
      >
        {temp}
      </Box>
    )
  }, [attachments, attachmentFiles, classes?.attachment])

  const handleClickFileIconButton = (fileType) => {
    setOpenSearchGif(false)
    setAcceptFileType(fileType)
    setOpenFileDialog(true)
  }

  const onInputChangeHandler = (field, editorCurrentContent) => {
    field.onChange(editorCurrentContent.getPlainText())
    const editorMentions: string[] = mentionGetEditorMentions(editorCurrentContent)
    if (JSON.stringify(mentionedUsers) !== JSON.stringify(editorMentions)) {
      // update only if the mentions changed
      setMentionedUsers(editorMentions)
    }
  }

  const editorHasNoData = isEditorEmpty(editorState)

  const handleEditorStateChange = (updatedEditorState) => {
    setEditorState(updatedEditorState)
  }

  const replaceTextAtEntity = (content, newText, targetEntity, targetEntityKey) => {
    let replacedContent = content

    content.getBlockMap().forEach((block) => {
      const blockKey = block.getKey()

      block.findEntityRanges(
        (character) => {
          const entityKey = character.getEntity()
          if (entityKey === null) return false
          return entityKey === targetEntityKey
        },
        (start, end) => {
          const entitySelection = new SelectionState({
            anchorKey: blockKey,
            anchorOffset: start,
            focusKey: blockKey,
            focusOffset: end,
          })

          replacedContent = Modifier.replaceText(replacedContent, entitySelection, newText, null, targetEntityKey)
        },
      )
    })
    return replacedContent
  }

  function createOrUpdateLinkAtSelection(editorState: EditorState, text: string, url: string): EditorState {
    // Create or update existing link
    const contentState = editorState.getCurrentContent()

    const entity = draftUtils.getCurrentEntity(editorState)
    if (entity && entity.get('type').toLowerCase() === 'link') {
      const entityKey = draftUtils.getCurrentEntityKey(editorState)
      const updatedContentState = contentState.mergeEntityData(
        entityKey,
        { url: url }, // Replace this with the new data you want to set
      )
      const finalContentState = replaceTextAtEntity(updatedContentState, text, entity, entityKey)
      const updatedEditorState = EditorState.push(editorState, finalContentState, 'apply-entity')
      const selectionState = updatedEditorState.getSelection()
      return EditorState.forceSelection(updatedEditorState, selectionState)
    }

    // Create new link, if there is a selection replace with the text
    const selectionState = editorState.getSelection()
    const contentStateWithEntity = contentState.createEntity('LINK', 'MUTABLE', {
      url,
      preview: true,
    })
    const entityKey = contentStateWithEntity.getLastCreatedEntityKey()

    if (text.trim() === '') {
      text = url
    }

    let contentStateWithText
    if (selectionState.isCollapsed()) {
      contentStateWithText = Modifier.insertText(
        contentStateWithEntity,
        selectionState,
        text,
        null, // no inline style
        entityKey, // entity associated with inserted text
      )
    } else {
      contentStateWithText = Modifier.replaceText(
        contentStateWithEntity,
        selectionState,
        text,
        null, // no inline style
        entityKey, // entity associated with inserted text
      )
    }

    const newEditorState = EditorState.push(editorState, contentStateWithText, 'insert-characters')

    const newSelectionState = newEditorState.getSelection().merge({
      anchorOffset: selectionState.getStartOffset() + text.length,
      focusOffset: selectionState.getStartOffset() + text.length,
    })

    const finalEditorState = EditorState.forceSelection(newEditorState, newSelectionState)
    return finalEditorState
  }

  const handleLinkDialogSave = ({ text, url }) => {
    setEditorState((prevEditorState) => createOrUpdateLinkAtSelection(prevEditorState, text, url))
    setShowLinkDialog(false)
  }

  const handleLinkDialogCancel = () => {
    setShowLinkDialog(false)
  }

  function getCurrentSelectionText(editorState: EditorState) {
    const selection = editorState.getSelection()
    const startKey = selection.getStartKey()
    const endKey = selection.getEndKey()
    // If the selection spans across multiple blocks
    if (startKey !== endKey) {
      // Handle multi-block selection (simplified)
      return null
    }

    // If selection is in a single block
    const startOffset = selection.getStartOffset()
    const endOffset = selection.getEndOffset()
    const contentState = editorState.getCurrentContent()
    const blockWithSelection = contentState.getBlockForKey(startKey)
    const text = blockWithSelection.getText()
    const selectedText = text.slice(startOffset, endOffset)

    return selectedText
  }

  const computeIsCreateLinkButtonEnabled = (editorState: EditorState) => {
    if (!editorState) {
      return false
    }

    const entity = draftUtils.getCurrentEntity(editorState)
    if (entity) {
      const entityType = entity.get('type').toLowerCase()
      if (entityType === 'link') {
        return true
      }
    }

    // Do not allow to enter a link across multiple blocks
    const selection = editorState.getSelection()
    const startKey = selection.getStartKey()
    const endKey = selection.getEndKey()
    if (startKey !== endKey) {
      return false
    }

    return true
  }

  function getTextFromEntityKey(editorState, targetEntityKey) {
    const contentState = editorState.getCurrentContent()
    const blockMap = contentState.getBlockMap()

    let foundText = ''

    blockMap.forEach((block) => {
      const entityRanges = []

      // Find all entity ranges within the block
      block.findEntityRanges(
        (characterMetadata) => {
          const entityKey = characterMetadata.getEntity()
          return entityKey !== null && entityKey === targetEntityKey
        },
        (start, end) => {
          entityRanges.push({ start, end })
        },
      )

      // Extract text for each entity range found
      for (const { start, end } of entityRanges) {
        const textSegment = block.getText().slice(start, end)
        foundText += textSegment
      }
    })

    return foundText
  }

  const handleOpenLinkDialog = (editorState: EditorState) => {
    const text = getCurrentSelectionText(editorState)
    const entityKey = draftUtils.getCurrentEntityKey(editorState)
    if (entityKey) {
      const entity = draftUtils.getCurrentEntity(editorState)
      const entityType = entity.get('type').toLowerCase()
      if (entityType === 'link') {
        const text = getTextFromEntityKey(editorState, entityKey)
        const data = entity.get('data')
        setCurrentLinkText(text)
        setCurrentLinkUrl(data.url)
      }
    } else {
      const selectionState = editorState.getSelection()
      const anchorKey = selectionState.getAnchorKey()
      const currentContent = editorState.getCurrentContent()
      const currentBlock = currentContent.getBlockForKey(anchorKey)
      const start = selectionState.getStartOffset()
      const end = selectionState.getEndOffset()
      const selectedText = currentBlock.getText().slice(start, end)
      setCurrentLinkText(selectedText)
      setCurrentLinkUrl('')
    }
    setShowLinkDialog(true)
  }

  const renderBottomBar = (
    <div className={'w-full'}>
      <Divider sx={{ width: '108%', borderColor: isDarkTheme ? '#25262b' : '#ebebec' }} />

      <div className={'mt-2 flex w-full'}>
        <div className={'grow'}>
          <EmojiPickerWithButton
            className={classes.iconButton}
            onSelectedEmoji={(e) => insertEmoji(e, editorState, setEditorState)}
            sx={{
              color: isDarkTheme ? '#8D94A3' : '#585D66',
            }}
          />
          &nbsp;&nbsp;
          <IconButton
            className={classes.iconButton}
            sx={{
              color: isDarkTheme ? '#8D94A3' : '#585D66',
            }}
            disabled={!computeIsCreateLinkButtonEnabled(editorState)}
            onClick={() => handleOpenLinkDialog(editorState)}
            size="small"
            aria-label="file upload"
            data-cy="post-edit-link-button"
          >
            <AddLinkIcon />
          </IconButton>
          &nbsp;&nbsp;
          <IconButton
            className={classes.iconButton}
            sx={{
              color: isDarkTheme ? '#8D94A3' : '#585D66',
            }}
            size="small"
            onClick={() => handleClickFileIconButton(IMAGE_ACCEPT_ONLY)}
            aria-label="show filestack"
            data-cy="post-edit-image-file-button"
          >
            <SVGPhotoNew />
          </IconButton>
          &nbsp;&nbsp;
          <IconButton
            className={classes.iconButton}
            sx={{
              color: isDarkTheme ? '#8D94A3' : '#585D66',
            }}
            onClick={() => handleClickFileIconButton(VIDEO_ACCEPT_ONLY)}
            size="small"
            aria-label="video file upload"
            data-cy="post-edit-video-file-button"
          >
            <SVGVideoNew />
          </IconButton>
          &nbsp;&nbsp;
          <IconButton
            className={classes.iconButton}
            sx={{
              color: isDarkTheme ? '#8D94A3' : '#585D66',
            }}
            onClick={() =>
              handleClickFileIconButton({
                'application/pdf': ['.pdf'],
                'application/zip': ['.zip'],
                'application/vnd.ms-excel': ['.xls'],
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
                'application/vnd.ms-powerpoint': ['.ppt', '.pptx'],
                'application/msword': ['.doc'],
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
                'text/*': [],
              })
            }
            size="small"
            aria-label="file upload"
            data-cy="post-edit-file-button"
          >
            <SVGFileNew />
          </IconButton>
          &nbsp;&nbsp;
          <IconButton
            className={openSearchGif ? classes.selectedIconButton : classes.iconButton}
            sx={{
              color: isDarkTheme ? '#8D94A3' : '#585D66',
            }}
            onClick={() => {
              setOpenSearchGif(true)
            }}
            size="small"
            aria-label="show gif search"
            data-cy="post-edit-gif-search-button"
          >
            <SVGGifNew />
          </IconButton>
        </div>
        <Button
          type="submit"
          disabled={
            !isDirty ||
            !isValid ||
            editorHasNoData ||
            requestUpsertFeed ||
            (!title && !attachmentFiles.length) ||
            Boolean(uploadProgress)
          }
          data-cy="post-edit-submit-button"
          loading={requestUpsertFeed || Boolean(uploadProgress)}
        >
          {data ? 'Save' : 'Publish'}
        </Button>
      </div>
    </div>
  )

  const handleSpaceChange = (space: any) => {
    if (space) {
      setSelectedSpace(space)
      setError(null)
    }
  }

  const renderInnerContent = (
    <div>
      <Box
        id="create-post-dialog-title"
        sx={{
          borderBottom: 'none',
          textAlign: 'right',
          //position: 'initial'
          //bottom: 425,
          //right: 0,
        }}
      >
        <div id="portal-suggestions" />
        <AddLinkDialog
          open={showLinkDialog}
          text={currentLinkText}
          url={currentLinkUrl}
          onCancel={handleLinkDialogCancel}
          onSave={handleLinkDialogSave}
        />
        <IconButton
          sx={{
            background: isDarkTheme ? '#17171a' : '#ffffff',
            color: '#8D94A3',
            p: '14px',
            m: '6px',
            width: '40px',
            height: '40px',
            '&.MuiIconButton-root': {
              position: isMobile ? 'initial !important' : '',
            },
          }}
          size="medium"
          aria-label="close"
          className="close large"
          onClick={() => {
            if (mode === 'create') {
              dispatch(
                setCurrentPostDraft({
                  editorState: editorState,
                  title: title,
                  attachments: attachments,
                  attachment_files: attachmentFiles,
                }),
              )
            }
            onClose()
          }}
        >
          <SVGCloseNew fontSize={16} />
        </IconButton>
      </Box>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          m: 'auto',
        }}
      >
        <div>
          <Box
            sx={{
              position: 'relative',
              backgroundColor: theme.palette.background.paper,
              m: 'auto',
              width: '100%',
              maxWidth: isMobile ? '100vw' : 640,
              overflow: 'hidden',
              borderRadius: isMobile ? '12px 12px 0px 0px' : 4,
              pt: '16px',
              pl: '24px',
              pr: '24px',
              pb: '9px',
            }}
          >
            <form autoComplete="off" onSubmit={handleSubmit(handleFormSubmit)}>
              <input {...getInputProps()} />
              <input type="hidden" {...register('attachments')} />
              <input type="hidden" {...register('attachment_files')} />
              <Grid container sx={{ position: 'relative' }}>
                <div className={'mb-4 flex items-center gap-4'}>
                  {user && (
                    <AppProfileImage
                      imageUrl={userProfile?.image || user?.image}
                      cropArea={userProfile?.image_crop_area || user?.image_crop_area}
                      name={userFullName}
                      size={40}
                    />
                  )}

                  <div className={''}>
                    <div className={'flex items-center'}>
                      <span
                        className={'whitespace-nowrap text-sm font-bold'}
                        style={{
                          color: isDarkTheme ? 'rgba(255, 255, 255, 0.87)' : '#000000',
                        }}
                      >
                        {userFullName}
                      </span>
                      {isMobile && <br />}
                      <span className={'whitespace-nowrap'}>
                        &nbsp; {mode === 'edit' ? 'editing post in' : 'posting in'}&nbsp;
                      </span>
                      <span>
                        <SpaceSelector
                          hasErrors={Boolean(error)}
                          spaces={spaces}
                          selectedSpace={selectedSpace}
                          onSelectionChange={handleSpaceChange}
                          disabled={mode === 'edit'}
                        />
                      </span>
                    </div>
                  </div>
                </div>

                {!isMobile && (
                  <Grid item xs={12} sx={{ position: 'relative', left: '-24px' }}>
                    <Divider sx={{ width: '108%', borderColor: isDarkTheme ? '#25262b' : '#ebebec' }} />
                  </Grid>
                )}
                <Grid item xs={12}>
                  <Grid container>
                    <Grid
                      item
                      xs={12}
                      sx={{
                        ml: '2px',
                        '&:focusVisible': {
                          outline: 'none',
                        },
                      }}
                      {...getRootProps()}
                    >
                      <Grid item xs={12}>
                        {openSearchGif ? (
                          <ClickAwayListener
                            onClickAway={(e) => {
                              setOpenSearchGif(false)
                            }}
                          >
                            <div>
                              <Box
                                sx={{
                                  backgroundColor: theme.palette.background.paper,
                                  borderRadius: '16px',
                                  border: '1px solid',
                                  borderColor: isDarkTheme ? '#24252a' : '#ebebec',
                                  boxShadow: isDarkTheme ? 'none' : '2px 0px 8px rgba(0, 0, 0, 0.10)',
                                  position: 'relative',
                                  overflow: 'visible',
                                  bottom: '-8px',
                                  width: '100%',
                                  maxWidth: isMobile ? '100vw' : 600,
                                  height: isMobile ? '45vh' : 432,
                                  zIndex: 1,
                                }}
                              >
                                <AppSearchGif
                                  onSelectGif={(gif) => {
                                    const temp = getValues('attachment_files')
                                    setValue('attachment_files', [...temp, gif], {
                                      shouldValidate: true,
                                      shouldDirty: true,
                                    })
                                    setOpenSearchGif(false)
                                  }}
                                  onClose={() => setOpenSearchGif(false)}
                                />
                              </Box>
                            </div>
                          </ClickAwayListener>
                        ) : (
                          <div>
                            <Controller
                              render={({ field: { onBlur, ...rest }, fieldState: { error } }) => (
                                <FormControl
                                  error={Boolean(error)}
                                  className="form-control"
                                  fullWidth
                                  sx={{
                                    '& .MuiOutlinedInput-root': {
                                      fontSize: 18,
                                      fontFamily: 'Graphik SemiBold',
                                      border: 'none',
                                      fontWeight: 700,
                                      p: '22px 0px 14px 0px',
                                    },
                                    '& .MuiFormHelperText-root': {
                                      display: 'none',
                                    },
                                    '& .MuiOutlinedInput-notchedOutline': {
                                      border: 'none',
                                    },
                                    '& .MuiOutlinedInput-multiline': {
                                      padding: 0,
                                    },
                                  }}
                                >
                                  <TextField
                                    {...rest}
                                    placeholder="Title"
                                    variant="outlined"
                                    error={Boolean(error)}
                                    helperText={error?.message}
                                    onBlur={(e) => {
                                      selectionRef.current = {
                                        start: e.target.selectionStart,
                                        end: e.target.selectionEnd,
                                      }
                                      onBlur()
                                    }}
                                    InputProps={{
                                      multiline: true,
                                      maxRows: 1,
                                      sx: {
                                        '& .MuiOutlinedInput-input': {
                                          whiteSpace: 'nowrap',
                                          overflowX: 'hidden',
                                          '&::placeholder': {
                                            color: isDarkTheme ? '#6f6f71 !important' : '#585D66 !important',
                                            opacity: 1,
                                          },
                                        },
                                      },
                                    }}
                                    data-cy="post-edit-title-field"
                                    inputProps={{ maxLength: 100 }}
                                    autoFocus
                                  />
                                </FormControl>
                              )}
                              control={control}
                              name="title"
                            />
                            <Box
                              sx={{
                                maxHeight: isMobile ? '30vh' : '50vh',
                                overflowY: 'auto',
                              }}
                            >
                              <Grid item xs={12}>
                                <Controller
                                  render={({ field, fieldState: { error } }) => (
                                    <FormControl
                                      error={Boolean(error)}
                                      className={clsx((classes as any).content, 'form-control')}
                                      fullWidth
                                      sx={{ color: isDarkTheme ? '#e1e1e1' : '#212121' }}
                                    >
                                      {editorState && (
                                        <MentionsInput
                                          editorState={editorState}
                                          isPosting={requestUpsertFeed}
                                          placeholder="Write something..."
                                          users={formattedSuggestions}
                                          onChange={(editorState) => {
                                            onInputChangeHandler(field, editorState)
                                          }}
                                          onChangeEditorState={handleEditorStateChange}
                                          portal={true}
                                          portalTargetId={'portal-suggestions'}
                                          focus={false}
                                        />
                                      )}
                                    </FormControl>
                                  )}
                                  control={control}
                                  name="text"
                                />
                              </Grid>
                              {error && (
                                <Grid item xs={12} sx={{ textAlign: 'right' }}>
                                  <Typography sx={{ color: 'red', paddingBottom: '6px' }}>{error}</Typography>
                                </Grid>
                              )}
                              <Grid item xs={12}>
                                {renderedLinkPreviews}
                              </Grid>
                              <Grid item xs={12}>
                                {renderAttachments}
                              </Grid>
                            </Box>
                          </div>
                        )}
                      </Grid>
                    </Grid>
                    {renderBottomBar}
                    <Divider />
                    {uploadProgress > 0 && (
                      <div item xs={12} sx={{ mt: '10px', mb: '5px' }}>
                        <LinearProgress variant="determinate" value={uploadProgress} />
                      </div>
                    )}
                  </Grid>
                </Grid>
              </Grid>
            </form>
          </Box>
        </div>
      </Box>
    </div>
  )

  if (!user?.id) return null

  return (
    <div>
      <Modal
        open={open}
        onClose={() => {
          if (openSearchGif) return
          if (mode === 'create') {
            dispatch(
              setCurrentPostDraft({
                editorState: editorState,
                title: title,
                attachments: attachments,
                attachment_files: attachmentFiles,
              }),
            )
          }
          onClose()
        }}
        aria-labelledby="create-post-dialog-title"
        sx={{
          '& .MuiDialog-paper': {
            backgroundColor: 'transparent',
            height: '100%',
            width: '100%',
            maxHeight: '100%',
            maxWidth: '100%',
            margin: 0,
            display: 'flex', // Added to make the content occupy full height
            flexDirection: 'column', // Added to make the content occupy full height
          },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {isMobile ? (
          <Slide direction="up" timeout={200} in={open}>
            <div className={classes.focusVisible} style={{ border: 'none', position: 'absolute', bottom: 0, right: 0 }}>
              {renderInnerContent}
            </div>
          </Slide>
        ) : (
          <div>{renderInnerContent}</div>
        )}
      </Modal>
    </div>
  )
}

export default EditPost
