import React from 'react'
import { SearchBox, useSearchBox } from 'react-instantsearch'

export {
  ALGOLIA_INDEX_ENUM,
  INDEX_NAME_MEMBER,
  INDEX_NAME_FEED,
  INDEX_NAME_CONTENT_LIBRARY,
} from '@/libs/algolia'

const SearchBoxWrapper = (props: any) => {
  const { setIsSearchBoxEmpty, refine, query } = props

  return (
    <SearchBox
      className="text-black text-xs"
      autoFocus
      placeholder="Search content or members..."
      onInput={() => {
        setIsSearchBoxEmpty((prevVal) => {
          if (query.length === 0) {
            return false
          }

          if (prevVal) {
            return true
          }
        })
        refine(query)
      }}
    />
  )
}

function CustomSearchBox(props: { setIsSearchBoxEmpty: any }) {
  const searchBoxApi = useSearchBox(props)

  return <SearchBoxWrapper {...searchBoxApi} setIsSearchBoxEmpty={props.setIsSearchBoxEmpty} />
}

export default CustomSearchBox
